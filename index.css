/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: #e2e8f0;
  background-color: #0f172a;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Focus styles */
input:focus,
select:focus,
button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #38bdf8;
}

/* Table responsive improvements */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
  }
  
  table {
    min-width: 640px;
  }
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.transaction-row {
  animation: slideIn 0.3s ease-out;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #38bdf8 0%, #0ea5e9 100%);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(56, 189, 248, 0.3);
}

/* Chart container improvements */
.recharts-wrapper {
  font-family: 'Inter', sans-serif !important;
}

.recharts-tooltip-wrapper {
  z-index: 1000;
}

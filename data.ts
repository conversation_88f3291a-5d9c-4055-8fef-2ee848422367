import { Transaction, TransactionType, TransactionStatus } from './types';

export const initialTransactions: Transaction[] = [
  // 01/07
  { id: 1, date: '2025-07-01', description: '<PERSON> 2/2', type: TransactionType.Receita, value: 449.00, status: TransactionStatus.Pendente },
  { id: 2, date: '2025-07-01', description: 'DEMARCHI BRASILIA 2/2', type: TransactionType.Receita, value: 1405.65, status: TransactionStatus.Pendente },
  { id: 3, date: '2025-07-01', description: '<PERSON><PERSON><PERSON> Gomes 2/2', type: TransactionType.Receita, value: 623.75, status: TransactionStatus.Pendente },
  { id: 4, date: '2025-07-01', description: 'MELISSA BRUM PEREIRA DA MATA LTDA 2/2', type: TransactionType.Receita, value: 523.50, status: TransactionStatus.Pendente },
  { id: 5, date: '2025-07-01', description: 'CONSTRUTORA ENGEMEGA 2/2', type: TransactionType.Receita, value: 478.50, status: TransactionStatus.Pendente },
  { id: 6, date: '2025-07-01', description: 'HI-COM COMUNICACAO DE DADOS 1/2', type: TransactionType.Receita, value: 1017.00, status: TransactionStatus.Pendente },
  { id: 7, date: '2025-07-01', description: 'Estorno Parcial venda 2265', type: TransactionType.Extra, value: 445.00, status: TransactionStatus.Pendente },
  // 02/07
  { id: 8, date: '2025-07-02', description: 'FINATEC', type: TransactionType.Receita, value: 7580.00, status: TransactionStatus.Pendente },
  { id: 9, date: '2025-07-02', description: 'Roberto Bittar Bigonha 2/2', type: TransactionType.Receita, value: 642.60, status: TransactionStatus.Pendente },
  { id: 10, date: '2025-07-02', description: 'DEMARCHI BRASILIA', type: TransactionType.Receita, value: 1125.60, status: TransactionStatus.Pendente },
  { id: 11, date: '2025-07-02', description: 'Fornecedor Fernanda Couros', type: TransactionType.Extra, value: 195.00, status: TransactionStatus.Pendente },
  { id: 12, date: '2025-07-02', description: 'Rescisão Jacilene', type: TransactionType.Extra, value: 766.68, status: TransactionStatus.Pendente },
  { id: 13, date: '2025-07-02', description: 'Rescisão Valdira', type: TransactionType.Extra, value: 741.35, status: TransactionStatus.Pendente },
  { id: 14, date: '2025-07-02', description: 'Reposição de Saldo BE', type: TransactionType.Extra, value: 42.02, status: TransactionStatus.Pendente },
  // 03/07
  { id: 15, date: '2025-07-03', description: 'João Carlos Lobo Sousa Monteiro 2/2', type: TransactionType.Receita, value: 978.01, status: TransactionStatus.Pendente },
  { id: 16, date: '2025-07-03', description: 'V12 MOTORS 2/2', type: TransactionType.Receita, value: 803.25, status: TransactionStatus.Pendente },
  { id: 17, date: '2025-07-03', description: 'V12 MOTORS 2/2', type: TransactionType.Receita, value: 1138.00, status: TransactionStatus.Pendente },
  { id: 18, date: '2025-07-03', description: 'Vivo (Plano Celulares)', type: TransactionType.Despesa, value: 317.03, status: TransactionStatus.Pendente },
  // 04/07
  { id: 19, date: '2025-07-04', description: 'BRUNA RODRIGUES DOS SANTOS 2/2', type: TransactionType.Receita, value: 634.80, status: TransactionStatus.Pendente },
  { id: 20, date: '2025-07-04', description: 'IGREJA CRISTA EVANGELICA DE BRASILIA 2/2', type: TransactionType.Receita, value: 4033.10, status: TransactionStatus.Pendente },
  { id: 21, date: '2025-07-04', description: 'Luisa Correa 2/2', type: TransactionType.Receita, value: 565.65, status: TransactionStatus.Pendente },
  { id: 22, date: '2025-07-04', description: 'DARA CRISTINA FERNANDES 2/2', type: TransactionType.Receita, value: 1084.60, status: TransactionStatus.Pendente },
  { id: 23, date: '2025-07-04', description: 'R.T.K 2/2', type: TransactionType.Receita, value: 1601.10, status: TransactionStatus.Pendente },
  { id: 24, date: '2025-07-04', description: 'OPUS SOLUCOES ARQUITETONICAS', type: TransactionType.Receita, value: 628.50, status: TransactionStatus.Pendente },
  { id: 25, date: '2025-07-04', description: 'Seguro CONTIL CONSTRUÇÃO - 3/5', type: TransactionType.Despesa, value: 94.00, status: TransactionStatus.Pendente },
  { id: 26, date: '2025-07-04', description: 'CONDOMINIO PRAIAS DO LAGO RESORT', type: TransactionType.Despesa, value: 211.16, status: TransactionStatus.Pendente },
  { id: 27, date: '2025-07-04', description: 'Folha de pagamento', type: TransactionType.Despesa, value: 20575.10, status: TransactionStatus.Pendente },
  { id: 28, date: '2025-07-04', description: 'Premiação', type: TransactionType.Despesa, value: 0, status: TransactionStatus.Pendente },
  { id: 29, date: '2025-07-04', description: 'Facção Patricia', type: TransactionType.Extra, value: 500.00, status: TransactionStatus.Pendente },
  { id: 30, date: '2025-07-04', description: 'Filipe De Almeida Do Amaral ??', type: TransactionType.Extra, value: 270.00, status: TransactionStatus.Pendente },
  // 07/07
  { id: 31, date: '2025-07-07', description: 'WESLLEY FERNANDES PAIVA 2/2', type: TransactionType.Receita, value: 4535.00, status: TransactionStatus.Pendente },
  { id: 32, date: '2025-07-07', description: 'PREFEITOS DO FUTURO 2/2', type: TransactionType.Receita, value: 6300.00, status: TransactionStatus.Pendente },
  { id: 33, date: '2025-07-07', description: 'MATOS NOVIDADES 2/2', type: TransactionType.Receita, value: 598.50, status: TransactionStatus.Pendente },
  { id: 34, date: '2025-07-07', description: 'CARLIN MALHAS ref. 02/2025 - parc. 4/4', type: TransactionType.Despesa, value: 1957.80, status: TransactionStatus.Pendente },
  { id: 35, date: '2025-07-07', description: 'FAEDDA IMPORTACAO ref. 3495 - parc. 3/3', type: TransactionType.Despesa, value: 1332.64, status: TransactionStatus.Pendente },
  { id: 36, date: '2025-07-07', description: 'Pg Personalizados Ltda', type: TransactionType.Extra, value: 562.00, status: TransactionStatus.Pendente },
  { id: 37, date: '2025-07-07', description: 'Mundial Atacadista', type: TransactionType.Extra, value: 1112.21, status: TransactionStatus.Pendente },
];

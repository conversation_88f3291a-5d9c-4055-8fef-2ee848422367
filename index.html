<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Financial Flow Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'brand-primary': '#0f172a',
              'brand-secondary': '#1e293b',
              'brand-accent': '#38bdf8',
              'brand-text': '#e2e8f0',
              'brand-subtle': '#94a3b8',
              'brand-success': '#22c55e',
              'brand-warning': '#f59e0b',
              'brand-danger': '#ef4444',
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
          },
        },
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.1/",
    "react": "https://esm.sh/react@^19.1.1",
    "react-dom/": "https://esm.sh/react-dom@^19.1.1/",
    "recharts": "https://esm.sh/recharts@^3.1.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-brand-primary">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>

import React from 'react';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  colorClass: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, colorClass }) => {
  return (
    <div className="bg-brand-secondary p-6 rounded-xl shadow-sm border border-slate-700/30 flex items-center space-x-4 transition-all duration-200 hover:shadow-md hover:border-slate-600/50">
      <div className={`p-3 rounded-lg ${colorClass} shadow-sm`}>
        {icon}
      </div>
      <div className="flex-1">
        <p className="text-sm text-brand-subtle font-medium">{title}</p>
        <p className="text-2xl font-semibold text-brand-text">{value}</p>
      </div>
    </div>
  );
};

export default StatCard;


import React from 'react';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  colorClass: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, colorClass }) => {
  return (
    <div className="bg-gradient-to-br from-brand-secondary to-slate-800 p-6 rounded-2xl shadow-lg border border-slate-700/50 flex items-center space-x-4 transition-all duration-300 hover:scale-105 hover:shadow-xl hover:border-slate-600 group">
      <div className={`p-3 rounded-xl ${colorClass} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
        {icon}
      </div>
      <div className="flex-1">
        <p className="text-sm text-brand-subtle font-medium uppercase tracking-wider">{title}</p>
        <p className="text-2xl font-bold text-brand-text group-hover:text-white transition-colors duration-300">{value}</p>
      </div>
      <div className="w-1 h-12 bg-gradient-to-b from-transparent via-brand-accent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full"></div>
    </div>
  );
};

export default StatCard;

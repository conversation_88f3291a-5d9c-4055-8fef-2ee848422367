import React, { useMemo } from 'react';
import { Transaction, TransactionType, Kpis } from '../types';
import StatCard from './StatCard';
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon, ScaleIcon, DocumentChartBarIcon } from './icons';
import { PieChart, Pie, Cell, ResponsiveContainer, Bar, XAxis, YAxis, Tooltip, CartesianGrid, ComposedChart, Line, Legend } from 'recharts';

interface DashboardProps {
  transactions: Transaction[];
  kpis: Kpis;
  dateFilteredTransactions: Transaction[];
  dateFilteredKpis: Kpis;
  showPeriodData?: boolean;
}

const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
};

const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const balance = payload.find((p: any) => p.dataKey === 'balance');
      const dailyNet = payload.find((p: any) => p.dataKey === 'dailyNet');

      return (
        <div className="p-3 bg-brand-secondary border border-slate-700 rounded-lg shadow-lg text-sm">
          <p className="label text-brand-subtle font-semibold mb-2">{`Data: ${label}`}</p>
          {dailyNet && <p className={`intro font-medium ${dailyNet.value >= 0 ? 'text-brand-success' : 'text-brand-danger'}`}>{`Fluxo do Dia: ${formatCurrency(dailyNet.value)}`}</p>}
          {balance && <p className="intro text-brand-text font-medium">{`Saldo Acumulado: ${formatCurrency(balance.value)}`}</p>}
        </div>
      );
    }
    return null;
  };

const Dashboard: React.FC<DashboardProps> = ({
  transactions,
  kpis,
  dateFilteredTransactions,
  dateFilteredKpis,
  showPeriodData = true
}) => {

  // Usar dados filtrados por período para os gráficos
  const chartTransactions = showPeriodData ? dateFilteredTransactions : transactions;
  const chartKpis = showPeriodData ? dateFilteredKpis : kpis;

  const expenseData = useMemo(() => {
    const despesas = chartTransactions
      .filter(t => t.type === TransactionType.Despesa)
      .reduce((acc, t) => acc + t.value, 0);
    const extras = chartTransactions
      .filter(t => t.type === TransactionType.Extra)
      .reduce((acc, t) => acc + t.value, 0);

    return [
      { name: 'Despesas', value: despesas },
      { name: 'Extras', value: extras },
    ].filter(d => d.value > 0);
  }, [chartTransactions]);

  const balanceEvolutionData = useMemo(() => {
    const dailyData = new Map<string, number>();

    chartTransactions.forEach(t => {
      const value = t.type === TransactionType.Receita ? t.value : -t.value;
      const dateKey = t.date.split('/').reverse().join('-'); // YYYY-MM-DD for correct sorting
      dailyData.set(dateKey, (dailyData.get(dateKey) || 0) + value);
    });

    const sortedDays = Array.from(dailyData.keys()).sort();

    let runningBalance = 0;
    return sortedDays.map(dateKey => {
      const dailyNet = dailyData.get(dateKey) || 0;
      runningBalance += dailyNet;
      const dateParts = dateKey.split('-');
      return {
        date: `${dateParts[2]}/${dateParts[1]}`, // "DD/MM"
        balance: runningBalance,
        dailyNet: dailyNet
      };
    });
  }, [chartTransactions]);


  const PIE_COLORS = ['#ef4444', '#f59e0b'];

  return (
    <div className="p-4 sm:p-6 md:p-8">
      {/* KPIs Gerais */}
      <div className="mb-6">
        <h2 className="text-lg font-bold text-brand-text mb-4">Resumo Geral (Todos os Dados)</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard title="Total Recebido" value={formatCurrency(kpis.totalReceitas)} icon={<ArrowTrendingUpIcon className="w-6 h-6 text-white"/>} colorClass="bg-brand-success" />
          <StatCard title="Total Pago" value={formatCurrency(kpis.totalDespesas)} icon={<ArrowTrendingDownIcon className="w-6 h-6 text-white"/>} colorClass="bg-brand-danger" />
          <StatCard title="Saldo Final" value={formatCurrency(kpis.saldoFinal)} icon={<ScaleIcon className="w-6 h-6 text-white"/>} colorClass="bg-brand-accent" />
          <StatCard title="Transações" value={String(kpis.transacoes)} icon={<DocumentChartBarIcon className="w-6 h-6 text-white"/>} colorClass="bg-purple-500" />
        </div>
      </div>

      {/* KPIs do Período Filtrado */}
      {showPeriodData && (chartKpis.transacoes !== kpis.transacoes) && (
        <div className="mb-6">
          <h2 className="text-lg font-bold text-brand-text mb-4">Resumo do Período Filtrado</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard title="Recebido no Período" value={formatCurrency(chartKpis.totalReceitas)} icon={<ArrowTrendingUpIcon className="w-6 h-6 text-white"/>} colorClass="bg-green-600" />
            <StatCard title="Pago no Período" value={formatCurrency(chartKpis.totalDespesas)} icon={<ArrowTrendingDownIcon className="w-6 h-6 text-white"/>} colorClass="bg-red-600" />
            <StatCard title="Saldo do Período" value={formatCurrency(chartKpis.saldoFinal)} icon={<ScaleIcon className="w-6 h-6 text-white"/>} colorClass="bg-blue-600" />
            <StatCard title="Transações do Período" value={String(chartKpis.transacoes)} icon={<DocumentChartBarIcon className="w-6 h-6 text-white"/>} colorClass="bg-indigo-600" />
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
        <div className="lg:col-span-2 bg-brand-secondary rounded-2xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-brand-text mb-4">Evolução do Saldo</h2>
            <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={balanceEvolutionData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#334155" />
                    <XAxis dataKey="date" tick={{ fill: '#94a3b8' }} />
                    <YAxis yAxisId="left" tickFormatter={(value) => `R$${Number(value/1000).toFixed(0)}k`} tick={{ fill: '#94a3b8' }} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{ color: '#94a3b8' }} />
                    <Bar yAxisId="left" dataKey="dailyNet" name="Fluxo do Dia" barSize={20}>
                        {balanceEvolutionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.dailyNet >= 0 ? 'rgba(34, 197, 94, 0.6)' : 'rgba(239, 68, 68, 0.6)'} />
                        ))}
                    </Bar>
                    <Line yAxisId="left" type="monotone" dataKey="balance" name="Saldo Acumulado" stroke="#38bdf8" strokeWidth={2} dot={{ r: 4, strokeWidth: 2, fill: '#0f172a' }} />
                </ComposedChart>
            </ResponsiveContainer>
        </div>
        <div className="bg-brand-secondary rounded-2xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-brand-text mb-4">Distribuição de Gastos</h2>
            {expenseData.length > 0 ? (
                <>
                <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                        <Pie data={expenseData} dataKey="value" nameKey="name" cx="50%" cy="50%" innerRadius={60} outerRadius={100} fill="#8884d8" paddingAngle={5}>
                            {expenseData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                            ))}
                        </Pie>
                        <Tooltip formatter={(value: number) => formatCurrency(value)} contentStyle={{backgroundColor: '#1e293b', border: '1px solid #334155', borderRadius: '0.5rem'}} />
                    </PieChart>
                </ResponsiveContainer>
                <div className="flex justify-center space-x-4 mt-4 text-brand-subtle">
                    {expenseData.map((entry, index) => (
                        <div key={index} className="flex items-center">
                            <span className="w-3 h-3 rounded-full mr-2" style={{backgroundColor: PIE_COLORS[index % PIE_COLORS.length]}}></span>
                            {entry.name}
                        </div>
                    ))}
                </div>
                </>
            ) : (
                <div className="flex items-center justify-center h-full text-brand-subtle">
                    <p>Sem dados de gastos para exibir.</p>
                </div>
            )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
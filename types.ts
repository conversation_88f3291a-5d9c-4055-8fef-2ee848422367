export enum TransactionType {
  Receita = '<PERSON>ce<PERSON>',
  Despesa = 'Despesa',
  Extra = 'Extra',
}

export enum TransactionStatus {
  Pago = 'Pago',
  Pendente = 'Pendente',
  Atrasado = 'Atrasado',
}

export interface Transaction {
  id: number;
  date: string;
  description: string;
  type: TransactionType;
  value: number;
  status: TransactionStatus;
}

export interface Kpis {
  totalReceitas: number;
  totalDespesas: number;
  saldoFinal: number;
  transacoes: number;
}

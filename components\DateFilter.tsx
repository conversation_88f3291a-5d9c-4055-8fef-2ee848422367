import React from 'react';

interface DateFilter {
  startDate: string;
  endDate: string;
  preset: 'all' | 'currentMonth' | 'currentYear' | 'lastMonth' | 'last3Months' | 'custom';
}

interface DateFilterProps {
  dateFilter: DateFilter;
  onDateFilterChange: (filter: DateFilter) => void;
}

const DateFilterComponent: React.FC<DateFilterProps> = ({ dateFilter, onDateFilterChange }) => {
  const handlePresetChange = (preset: DateFilter['preset']) => {
    onDateFilterChange({
      ...dateFilter,
      preset,
      startDate: preset === 'custom' ? dateFilter.startDate : '',
      endDate: preset === 'custom' ? dateFilter.endDate : ''
    });
  };

  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    onDateFilterChange({
      ...dateFilter,
      [field]: value,
      preset: 'custom'
    });
  };

  const getPresetLabel = (preset: string) => {
    switch (preset) {
      case 'all': return 'Todos os Períodos';
      case 'currentMonth': return 'Mês Atual';
      case 'currentYear': return 'Ano Atual';
      case 'lastMonth': return 'Mês Passado';
      case 'last3Months': return 'Últimos 3 Meses';
      case 'custom': return 'Período Personalizado';
      default: return preset;
    }
  };

  const presets: DateFilter['preset'][] = ['all', 'currentMonth', 'currentYear', 'lastMonth', 'last3Months', 'custom'];

  return (
    <div className="bg-brand-secondary rounded-2xl shadow-lg p-4 sm:p-6 mb-6">
      <h3 className="text-lg font-bold text-brand-text mb-4">Filtro de Período para Gráficos</h3>
      
      <div className="flex flex-wrap gap-2 mb-4">
        {presets.map(preset => (
          <button
            key={preset}
            onClick={() => handlePresetChange(preset)}
            className={`px-4 py-2 text-sm font-semibold rounded-full transition-colors duration-200 ${
              dateFilter.preset === preset 
                ? 'bg-brand-accent text-white shadow-lg' 
                : 'bg-slate-700 text-brand-subtle hover:bg-slate-600'
            }`}
          >
            {getPresetLabel(preset)}
          </button>
        ))}
      </div>

      {dateFilter.preset === 'custom' && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-brand-subtle mb-2">
              Data Inicial
            </label>
            <input
              type="date"
              value={dateFilter.startDate}
              onChange={(e) => handleDateChange('startDate', e.target.value)}
              className="w-full bg-slate-700 p-3 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent text-brand-text"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-brand-subtle mb-2">
              Data Final
            </label>
            <input
              type="date"
              value={dateFilter.endDate}
              onChange={(e) => handleDateChange('endDate', e.target.value)}
              className="w-full bg-slate-700 p-3 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent text-brand-text"
            />
          </div>
        </div>
      )}

      {dateFilter.preset !== 'all' && (
        <div className="mt-4 p-3 bg-slate-700/50 rounded-lg">
          <p className="text-sm text-brand-subtle">
            <span className="font-medium text-brand-text">Período ativo:</span> {getPresetLabel(dateFilter.preset)}
            {dateFilter.preset === 'custom' && dateFilter.startDate && dateFilter.endDate && (
              <span> ({new Date(dateFilter.startDate).toLocaleDateString('pt-BR')} - {new Date(dateFilter.endDate).toLocaleDateString('pt-BR')})</span>
            )}
          </p>
        </div>
      )}
    </div>
  );
};

export default DateFilterComponent;

import React, { useState } from 'react';
import { Transaction, TransactionType, TransactionStatus } from '../types';
import TransactionRow from './TransactionRow';
import { PlusIcon } from './icons';

interface TransactionTableProps {
  transactions: Transaction[];
  onAddTransaction: (transaction: Omit<Transaction, 'id'>) => void;
  onUpdateTransaction: (transaction: Transaction) => void;
  onDeleteTransaction: (id: number) => void;
}

const TransactionTable: React.FC<TransactionTableProps> = ({ 
  transactions, 
  onAddTransaction,
  onUpdateTransaction,
  onDeleteTransaction,
}) => {
  const [newTransaction, setNewTransaction] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    type: TransactionType.Despesa,
    value: '',
    status: TransactionStatus.Pendente,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewTransaction(prev => ({ ...prev, [name]: value }));
  };

  const handleAdd = () => {
    if (!newTransaction.description || !newTransaction.value) {
      alert('Por favor, preencha a descrição e o valor.');
      return;
    }
    onAddTransaction({
      ...newTransaction,
      value: parseFloat(newTransaction.value),
    });
    setNewTransaction({
      date: new Date().toISOString().split('T')[0],
      description: '',
      type: TransactionType.Despesa,
      value: '',
      status: TransactionStatus.Pendente,
    });
  };

  const sortedTransactions = [...transactions].sort((a, b) => {
    const dateA = new Date(a.date.split('/').reverse().join('-'));
    const dateB = new Date(b.date.split('/').reverse().join('-'));
    return dateB.getTime() - dateA.getTime();
  });
  
  const InputField = ({...props}) => <input {...props} className="w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;
  const SelectField = ({...props}) => <select {...props} className="w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;

  return (
    <div className="overflow-x-auto">
      <table className="w-full text-left text-sm min-w-[800px]">
          <thead className="bg-slate-700/50 border-b border-slate-600">
            <tr>
              <th className="p-3 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-left">Data</th>
              <th className="p-3 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-left">Descrição</th>
              <th className="p-3 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-left">Tipo</th>
              <th className="p-3 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-left">Status</th>
              <th className="p-3 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-right">Valor</th>
              <th className="p-3 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-center">Ações</th>
            </tr>
          </thead>
          <tbody>
            {sortedTransactions.map((transaction) => (
              <TransactionRow 
                key={transaction.id} 
                transaction={transaction}
                onUpdate={onUpdateTransaction}
                onDelete={onDeleteTransaction}
              />
            ))}
          </tbody>
          <tfoot>
            <tr className="border-t-2 border-slate-600 bg-slate-700/30">
                <td className="p-3"><InputField type="date" name="date" value={newTransaction.date} onChange={handleInputChange} /></td>
                <td className="p-3"><InputField type="text" name="description" placeholder="Descrição da nova transação..." value={newTransaction.description} onChange={handleInputChange} /></td>
                <td className="p-3">
                    <SelectField name="type" value={newTransaction.type} onChange={handleInputChange}>
                        {Object.values(TransactionType).map(type => <option key={type} value={type}>{type}</option>)}
                    </SelectField>
                </td>
                <td className="p-3">
                    <SelectField name="status" value={newTransaction.status} onChange={handleInputChange}>
                        {Object.values(TransactionStatus).map(status => <option key={status} value={status}>{status}</option>)}
                    </SelectField>
                </td>
                <td className="p-3"><InputField type="number" name="value" placeholder="0.00" value={newTransaction.value} onChange={handleInputChange} className="text-right w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" step="0.01" /></td>
                <td className="p-3 text-center">
                    <button
                      onClick={handleAdd}
                      className="bg-brand-accent hover:bg-sky-500 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center w-full transition-colors duration-200"
                      title="Adicionar nova transação"
                    >
                        <PlusIcon className="w-4 h-4" />
                        <span className="ml-2">Adicionar</span>
                    </button>
                </td>
            </tr>
          </tfoot>
        </table>
    </div>
  );
};

export default TransactionTable;
import React, { useState } from 'react';
import { Transaction, TransactionType, TransactionStatus } from '../types';
import TransactionRow from './TransactionRow';
import { PlusIcon } from './icons';

interface TransactionTableProps {
  transactions: Transaction[];
  onAddTransaction: (transaction: Omit<Transaction, 'id'>) => void;
  onUpdateTransaction: (transaction: Transaction) => void;
  onDeleteTransaction: (id: number) => void;
}

const TransactionTable: React.FC<TransactionTableProps> = ({ 
  transactions, 
  onAddTransaction,
  onUpdateTransaction,
  onDeleteTransaction,
}) => {
  const [newTransaction, setNewTransaction] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    type: TransactionType.Despesa,
    value: '',
    status: TransactionStatus.Pendente,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewTransaction(prev => ({ ...prev, [name]: value }));
  };

  const handleAdd = () => {
    if (!newTransaction.description || !newTransaction.value) {
      alert('Por favor, preencha a descrição e o valor.');
      return;
    }
    onAddTransaction({
      ...newTransaction,
      value: parseFloat(newTransaction.value),
    });
    setNewTransaction({
      date: new Date().toISOString().split('T')[0],
      description: '',
      type: TransactionType.Despesa,
      value: '',
      status: TransactionStatus.Pendente,
    });
  };

  const sortedTransactions = [...transactions].sort((a, b) => {
    const dateA = new Date(a.date.split('/').reverse().join('-'));
    const dateB = new Date(b.date.split('/').reverse().join('-'));
    return dateB.getTime() - dateA.getTime();
  });
  
  const InputField = ({...props}) => <input {...props} className="w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;
  const SelectField = ({...props}) => <select {...props} className="w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;

  return (
    <div className="bg-brand-secondary rounded-2xl shadow-lg p-4 sm:p-6">
      <h2 className="text-lg font-bold text-brand-text mb-4 flex items-center">
        <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
        Transações Recentes
      </h2>
      <div className="overflow-x-auto max-h-[600px] overflow-y-auto">
        <table className="w-full text-left text-sm">
          <thead className="sticky top-0 bg-brand-secondary z-10 border-b border-slate-600">
            <tr>
              <th className="p-2 text-xs font-semibold text-brand-subtle uppercase tracking-wider">Data</th>
              <th className="p-2 text-xs font-semibold text-brand-subtle uppercase tracking-wider">Descrição</th>
              <th className="p-2 text-xs font-semibold text-brand-subtle uppercase tracking-wider">Tipo</th>
              <th className="p-2 text-xs font-semibold text-brand-subtle uppercase tracking-wider">Status</th>
              <th className="p-2 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-right">Valor</th>
              <th className="p-2 text-xs font-semibold text-brand-subtle uppercase tracking-wider text-center">Ações</th>
            </tr>
          </thead>
          <tbody>
            {sortedTransactions.map((transaction) => (
              <TransactionRow 
                key={transaction.id} 
                transaction={transaction}
                onUpdate={onUpdateTransaction}
                onDelete={onDeleteTransaction}
              />
            ))}
          </tbody>
          <tfoot>
            <tr className="border-t-2 border-brand-accent/30 bg-gradient-to-r from-brand-accent/5 to-green-500/5">
                <td className="p-2"><InputField type="date" name="date" value={newTransaction.date} onChange={handleInputChange} /></td>
                <td className="p-2"><InputField type="text" name="description" placeholder="Descrição da nova transação..." value={newTransaction.description} onChange={handleInputChange} /></td>
                <td className="p-2">
                    <SelectField name="type" value={newTransaction.type} onChange={handleInputChange}>
                        {Object.values(TransactionType).map(type => <option key={type} value={type}>{type}</option>)}
                    </SelectField>
                </td>
                <td className="p-2">
                    <SelectField name="status" value={newTransaction.status} onChange={handleInputChange}>
                        {Object.values(TransactionStatus).map(status => <option key={status} value={status}>{status}</option>)}
                    </SelectField>
                </td>
                <td className="p-2"><InputField type="number" name="value" placeholder="0.00" value={newTransaction.value} onChange={handleInputChange} className="text-right w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" step="0.01" /></td>
                <td className="p-2 text-center">
                    <button
                      onClick={handleAdd}
                      className="bg-gradient-to-r from-brand-accent to-green-500 hover:from-sky-500 hover:to-green-400 text-white font-bold py-2 px-3 rounded-lg flex items-center justify-center w-full transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                      title="Adicionar nova transação"
                    >
                        <PlusIcon className="w-4 h-4" />
                        <span className="ml-1 hidden sm:inline">Adicionar</span>
                    </button>
                </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  );
};

export default TransactionTable;
import React, { useState } from 'react';
import { Transaction, TransactionType, TransactionStatus } from '../types';
import TransactionRow from './TransactionRow';
import { PlusIcon } from './icons';

interface TransactionTableProps {
  transactions: Transaction[];
  onAddTransaction: (transaction: Omit<Transaction, 'id'>) => void;
  onUpdateTransaction: (transaction: Transaction) => void;
  onDeleteTransaction: (id: number) => void;
}

const TransactionTable: React.FC<TransactionTableProps> = ({ 
  transactions, 
  onAddTransaction,
  onUpdateTransaction,
  onDeleteTransaction,
}) => {
  const [newTransaction, setNewTransaction] = useState({
    date: new Date().toISOString().split('T')[0],
    description: '',
    type: TransactionType.Despesa,
    value: '',
    status: TransactionStatus.Pendente,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewTransaction(prev => ({ ...prev, [name]: value }));
  };

  const handleAdd = () => {
    if (!newTransaction.description || !newTransaction.value) {
      alert('Por favor, preencha a descrição e o valor.');
      return;
    }
    onAddTransaction({
      ...newTransaction,
      value: parseFloat(newTransaction.value),
    });
    setNewTransaction({
      date: new Date().toISOString().split('T')[0],
      description: '',
      type: TransactionType.Despesa,
      value: '',
      status: TransactionStatus.Pendente,
    });
  };

  const sortedTransactions = [...transactions].sort((a, b) => {
    const dateA = new Date(a.date.split('/').reverse().join('-'));
    const dateB = new Date(b.date.split('/').reverse().join('-'));
    return dateB.getTime() - dateA.getTime();
  });
  
  const InputField = ({...props}) => <input {...props} className="w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;
  const SelectField = ({...props}) => <select {...props} className="w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;

  return (
    <div className="bg-brand-secondary rounded-2xl shadow-lg p-4 sm:p-6 mt-8">
      <h2 className="text-xl font-bold text-brand-text mb-4">Detalhes das Transações</h2>
      <div className="overflow-x-auto">
        <table className="w-full min-w-[640px] text-left">
          <thead className="sticky top-0 bg-brand-secondary z-10">
            <tr>
              <th className="p-3 text-sm font-semibold text-brand-subtle w-1/12">Data</th>
              <th className="p-3 text-sm font-semibold text-brand-subtle w-4/12">Descrição</th>
              <th className="p-3 text-sm font-semibold text-brand-subtle w-1/12">Tipo</th>
              <th className="p-3 text-sm font-semibold text-brand-subtle w-1/12">Status</th>
              <th className="p-3 text-sm font-semibold text-brand-subtle w-2/12 text-right">Valor</th>
              <th className="p-3 text-sm font-semibold text-brand-subtle w-2/12 text-center">Ações</th>
            </tr>
          </thead>
          <tbody>
            {sortedTransactions.map((transaction) => (
              <TransactionRow 
                key={transaction.id} 
                transaction={transaction}
                onUpdate={onUpdateTransaction}
                onDelete={onDeleteTransaction}
              />
            ))}
          </tbody>
          <tfoot>
            <tr className="border-t-2 border-slate-700">
                <td className="p-2"><InputField type="date" name="date" value={newTransaction.date} onChange={handleInputChange} /></td>
                <td className="p-2"><InputField type="text" name="description" placeholder="Nova transação..." value={newTransaction.description} onChange={handleInputChange} /></td>
                <td className="p-2">
                    <SelectField name="type" value={newTransaction.type} onChange={handleInputChange}>
                        {Object.values(TransactionType).map(type => <option key={type} value={type}>{type}</option>)}
                    </SelectField>
                </td>
                <td className="p-2">
                    <SelectField name="status" value={newTransaction.status} onChange={handleInputChange}>
                        {Object.values(TransactionStatus).map(status => <option key={status} value={status}>{status}</option>)}
                    </SelectField>
                </td>
                <td className="p-2"><InputField type="number" name="value" placeholder="0.00" value={newTransaction.value} onChange={handleInputChange} className="text-right w-full bg-slate-700 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" /></td>
                <td className="p-2 text-center">
                    <button onClick={handleAdd} className="bg-brand-accent hover:bg-sky-500 text-white font-bold py-2 px-3 rounded-md flex items-center justify-center w-full transition-colors">
                        <PlusIcon className="w-5 h-5" />
                        <span className="ml-1 hidden sm:inline">Adicionar</span>
                    </button>
                </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  );
};

export default TransactionTable;
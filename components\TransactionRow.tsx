import React, { useState, useEffect } from 'react';
import { Transaction, TransactionType, TransactionStatus } from '../types';
import { PencilSquareIcon, TrashIcon, CheckIcon } from './icons';

interface TransactionRowProps {
  transaction: Transaction;
  onUpdate: (transaction: Transaction) => void;
  onDelete: (id: number) => void;
}

const TransactionRow: React.FC<TransactionRowProps> = ({ transaction, onUpdate, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState(transaction);

  useEffect(() => {
    if (!isEditing) {
      setEditData(transaction);
    }
  }, [transaction, isEditing]);

  const handleSave = () => {
    const updatedTransaction = {
      ...editData,
      value: Number(editData.value) || 0,
      date: editData.date.includes('/') ? new Date(editData.date.split('/').reverse().join('-')).toISOString().split('T')[0] : editData.date
    };

    // Feedback visual se mudou para "Pago"
    if (transaction.status !== TransactionStatus.Pago && editData.status === TransactionStatus.Pago) {
      // Adicionar classe de animação ou mostrar notificação
      console.log('✅ Transação marcada como paga!');
    }

    onUpdate(updatedTransaction);
    setIsEditing(false);
  };
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditData(prev => ({ ...prev, [name]: value }));
  };

  const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  };

  const formatDateForInput = (dateStr: string) => {
    if (!dateStr) return '';
    // Handles both YYYY-MM-DD and DD/MM/YYYY
    if (dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        return `${parts[2]}-${parts[1]}-${parts[0]}`;
      }
    }
    return dateStr;
  }
  
  const formatDateForDisplay = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr + 'T00:00:00'); // Assume local timezone
    return date.toLocaleDateString('pt-BR');
  }

  const getStatusClass = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.Pago: return 'bg-green-500/20 text-green-400 border border-green-500/30';
      case TransactionStatus.Pendente: return 'bg-orange-500/20 text-orange-400 border border-orange-500/30';
      case TransactionStatus.Atrasado: return 'bg-red-500/20 text-red-400 border border-red-500/30';
      default: return 'bg-slate-500/20 text-slate-400 border border-slate-500/30';
    }
  };
  
  const getTypeClass = (type: TransactionType) => {
    switch (type) {
      case TransactionType.Receita: return 'bg-green-500/20 text-green-400 border border-green-500/30';
      case TransactionType.Despesa: return 'bg-red-500/20 text-red-400 border border-red-500/30';
      case TransactionType.Extra: return 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30';
      default: return 'bg-slate-500/20 text-slate-400 border border-slate-500/30';
    }
  }
  
  const InputField = ({...props}) => <input {...props} className="w-full bg-slate-800 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;
  const SelectField = ({...props}) => <select {...props} className="w-full bg-slate-800 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" />;


  if (isEditing) {
    return (
      <tr className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 shadow-lg">
        <td className="p-2"><InputField type="date" name="date" value={formatDateForInput(editData.date)} onChange={handleInputChange} /></td>
        <td className="p-2"><InputField type="text" name="description" value={editData.description} onChange={handleInputChange} placeholder="Descrição da transação" /></td>
        <td className="p-2">
            <SelectField name="type" value={editData.type} onChange={handleInputChange}>
                {Object.values(TransactionType).map(type => <option key={type} value={type}>{type}</option>)}
            </SelectField>
        </td>
        <td className="p-2">
            <SelectField name="status" value={editData.status} onChange={handleInputChange}>
                {Object.values(TransactionStatus).map(status => <option key={status} value={status}>{status}</option>)}
            </SelectField>
        </td>
        <td className="p-2"><InputField type="number" name="value" value={editData.value} onChange={handleInputChange} className="w-full text-right bg-slate-800 p-2 rounded-md border border-slate-600 focus:outline-none focus:ring-2 focus:ring-brand-accent" step="0.01" /></td>
        <td className="p-2">
          <div className="flex items-center justify-center gap-1">
            <button
              onClick={handleSave}
              className="p-1.5 text-green-400 hover:text-green-300 hover:bg-green-500/10 rounded-lg transition-all"
              title="Salvar alterações"
            >
              <CheckIcon className="w-4 h-4"/>
            </button>
            <button
              onClick={() => setIsEditing(false)}
              className="p-1.5 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all"
              title="Cancelar edição"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" /></svg>
            </button>
          </div>
        </td>
      </tr>
    );
  }

  return (
    <tr className="border-t border-slate-700/50 hover:bg-slate-800/30 transition-all duration-200 group">
      <td className="p-2 text-xs text-brand-subtle font-medium">{formatDateForDisplay(transaction.date)}</td>
      <td className="p-2 text-sm text-brand-text font-medium truncate max-w-[200px]" title={transaction.description}>
        {transaction.description}
      </td>
      <td className="p-2">
        <span className={`px-2 py-1 rounded-lg text-xs font-semibold ${getTypeClass(transaction.type)}`}>
          {transaction.type}
        </span>
      </td>
      <td className="p-2">
         <span className={`px-2 py-1 rounded-lg text-xs font-semibold ${getStatusClass(transaction.status)}`}>
          {transaction.status}
        </span>
      </td>
      <td className={`p-2 text-sm font-bold text-right ${transaction.type === TransactionType.Receita ? 'text-green-400' : 'text-red-400'}`}>
        {formatCurrency(transaction.value)}
      </td>
      <td className="p-2">
        <div className="flex items-center justify-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={() => setIsEditing(true)}
              className="p-1.5 text-brand-accent hover:text-sky-400 hover:bg-sky-500/10 rounded-lg transition-all"
              title="Editar transação"
            >
              <PencilSquareIcon className="w-4 h-4"/>
            </button>
            <button
              onClick={() => onDelete(transaction.id)}
              className="p-1.5 text-brand-danger hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all"
              title="Excluir transação"
            >
              <TrashIcon className="w-4 h-4"/>
            </button>
        </div>
      </td>
    </tr>
  );
};

export default TransactionRow;

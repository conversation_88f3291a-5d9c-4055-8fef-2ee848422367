import React, { useState, useMemo } from 'react';
import { Transaction, TransactionType, Kpis } from './types';
import { initialTransactions } from './data';
import Dashboard from './components/Dashboard';
import TransactionTable from './components/TransactionTable';
import DateFilterComponent from './components/DateFilter';

type FilterType = TransactionType | 'All';

interface DateFilter {
  startDate: string;
  endDate: string;
  preset: 'all' | 'currentMonth' | 'currentYear' | 'lastMonth' | 'last3Months' | 'custom';
}

const App: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>(initialTransactions);
  const [activeFilter, setActiveFilter] = useState<FilterType>('All');
  const [dateFilter, setDateFilter] = useState<DateFilter>({
    startDate: '',
    endDate: '',
    preset: 'all'
  });
  
  const handleAddTransaction = (newTransactionData: Omit<Transaction, 'id'>) => {
    setTransactions(prev => [
      ...prev,
      { ...newTransactionData, id: Date.now() } // Use timestamp for unique ID
    ]);
  };

  const handleUpdateTransaction = (updatedTransaction: Transaction) => {
    setTransactions(prev => 
      prev.map(t => t.id === updatedTransaction.id ? updatedTransaction : t)
    );
  };

  const handleDeleteTransaction = (id: number) => {
    if (window.confirm('Tem certeza que deseja excluir esta transação?')) {
        setTransactions(prev => prev.filter(t => t.id !== id));
    }
  };

  // Função para filtrar transações por período
  const filterTransactionsByDate = (transactions: Transaction[], filter: DateFilter): Transaction[] => {
    if (filter.preset === 'all') return transactions;

    const now = new Date();
    let startDate: Date;
    let endDate: Date;

    switch (filter.preset) {
      case 'currentMonth':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'currentYear':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = now;
        break;
      case 'lastMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'last3Months':
        startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1);
        endDate = now;
        break;
      case 'custom':
        if (!filter.startDate || !filter.endDate) return transactions;
        startDate = new Date(filter.startDate);
        endDate = new Date(filter.endDate);
        break;
      default:
        return transactions;
    }

    return transactions.filter(transaction => {
      // Converter data do formato YYYY-MM-DD para Date
      const transactionDate = new Date(transaction.date + 'T00:00:00');
      return transactionDate >= startDate && transactionDate <= endDate;
    });
  };


  const filteredTransactions = useMemo(() => {
    if (activeFilter === 'All') {
      return transactions;
    }
    return transactions.filter(t => t.type === activeFilter);
  }, [transactions, activeFilter]);

  // Transações filtradas por período (para os gráficos)
  const dateFilteredTransactions = useMemo(() => {
    return filterTransactionsByDate(transactions, dateFilter);
  }, [transactions, dateFilter]);

  const kpis = useMemo<Kpis>(() => {
    const source = transactions; // KPIs should always reflect the total, not the filtered view.

    const totalReceitas = source
      .filter(t => t.type === TransactionType.Receita)
      .reduce((sum, t) => sum + t.value, 0);

    const totalDespesas = source
      .filter(t => t.type === TransactionType.Despesa || t.type === TransactionType.Extra)
      .reduce((sum, t) => sum + t.value, 0);

    return {
        totalReceitas,
        totalDespesas,
        saldoFinal: totalReceitas - totalDespesas,
        transacoes: source.length
    };
  }, [transactions]);

  // KPIs para o período filtrado (para os gráficos)
  const dateFilteredKpis = useMemo<Kpis>(() => {
    const source = dateFilteredTransactions;

    const totalReceitas = source
      .filter(t => t.type === TransactionType.Receita)
      .reduce((sum, t) => sum + t.value, 0);

    const totalDespesas = source
      .filter(t => t.type === TransactionType.Despesa || t.type === TransactionType.Extra)
      .reduce((sum, t) => sum + t.value, 0);

    return {
        totalReceitas,
        totalDespesas,
        saldoFinal: totalReceitas - totalDespesas,
        transacoes: source.length
    };
  }, [dateFilteredTransactions]);
  
  const filteredKpis = useMemo<Kpis>(() => {
    const source = filteredTransactions;

    const totalReceitas = source
      .filter(t => t.type === TransactionType.Receita)
      .reduce((sum, t) => sum + t.value, 0);
    
    const totalDespesas = source
      .filter(t => t.type === TransactionType.Despesa || t.type === TransactionType.Extra)
      .reduce((sum, t) => sum + t.value, 0);

    return {
        totalReceitas,
        totalDespesas,
        saldoFinal: totalReceitas - totalDespesas,
        transacoes: source.length
    };
  }, [filteredTransactions]);


  const FilterButton: React.FC<{filterType: FilterType, children: React.ReactNode}> = ({ filterType, children }) => (
    <button
      onClick={() => setActiveFilter(filterType)}
      className={`px-4 py-2 text-sm font-semibold rounded-full transition-colors duration-200 ${
        activeFilter === filterType 
          ? 'bg-brand-accent text-white shadow-lg' 
          : 'bg-brand-secondary text-brand-subtle hover:bg-slate-700'
      }`}
    >
      {children}
    </button>
  );

  return (
    <div className="min-h-screen bg-brand-primary text-brand-text font-sans">
      <header className="bg-brand-secondary/50 backdrop-blur-lg border-b border-slate-700 sticky top-0 z-20">
        <div className="container mx-auto px-4 sm:px-6 md:px-8">
            <div className="flex items-center justify-between h-16">
                <h1 className="text-xl sm:text-2xl font-bold text-brand-text">
                    Fluxo de Caixa <span className="text-brand-accent">Interativo</span>
                </h1>
            </div>
        </div>
      </header>

      <main className="container mx-auto px-1 sm:px-2 md:px-4 py-8">
        <Dashboard
          transactions={transactions}
          kpis={kpis}
          dateFilteredTransactions={dateFilteredTransactions}
          dateFilteredKpis={dateFilteredKpis}
        />

        <div className="px-4 sm:px-6 md:px-8">
          <DateFilterComponent
            dateFilter={dateFilter}
            onDateFilterChange={setDateFilter}
          />

          <div className="flex flex-wrap gap-2 mb-6">
            <FilterButton filterType="All">Todas as Transações ({transactions.length})</FilterButton>
            <FilterButton filterType={TransactionType.Receita}>Receitas</FilterButton>
            <FilterButton filterType={TransactionType.Despesa}>Despesas</FilterButton>
            <FilterButton filterType={TransactionType.Extra}>Extras</FilterButton>
          </div>

          <TransactionTable
            transactions={filteredTransactions}
            onAddTransaction={handleAddTransaction}
            onUpdateTransaction={handleUpdateTransaction}
            onDeleteTransaction={handleDeleteTransaction}
          />
        </div>

      </main>
    </div>
  );
};

export default App;
